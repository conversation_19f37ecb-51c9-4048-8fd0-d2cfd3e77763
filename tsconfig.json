{"$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"target": "ESNext", "lib": ["dom", "dom.iterable", "esnext", "webworker"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "baseUrl": ".", "types": ["vitest/globals", "@serwist/next/typings"], "paths": {"@/*": ["./src/*"], "~test-utils": ["./tests/utils.tsx"]}, "plugins": [{"name": "next"}]}, "exclude": ["node_modules", "public/sw.js", "apps/desktop"], "include": ["**/*.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "next-env.d.ts", "src", "tests", "vitest.config.ts"]}